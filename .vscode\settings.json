{"idf.pythonInstallPath": "D:\\esp32-idf-ahy\\5.3.2\\tools\\idf-python\\3.11.2\\python.exe", "idf.flashType": "UART", "idf.portWin": "COM15", "idf.openOcdConfigs": ["interface/ftdi/esp32_devkitj_v1.cfg", "target/esp32s3.cfg"], "idf.customExtraVars": {"OPENOCD_SCRIPTS": "d:\\esp32-idf-ahy\\5.3.2\\tools\\openocd-esp32\\v0.12.0-esp32-20241016/openocd-esp32/share/openocd/scripts", "IDF_CCACHE_ENABLE": "1", "ESP_ROM_ELF_DIR": "d:\\esp32-idf-ahy\\5.3.2\\tools\\esp-rom-elfs\\20240305/", "IDF_TARGET": "esp32s3"}, "clangd.path": "d:\\esp32-idf-ahy\\5.3.2\\tools\\esp-clang\\16.0.1-fe4f10a809\\esp-clang\\bin\\clangd.exe", "clangd.arguments": ["--background-index", "--query-driver=d:\\esp32-idf-ahy\\5.3.2\\tools\\xtensa-esp-elf\\esp-13.2.0_20240530\\xtensa-esp-elf\\bin\\xtensa-esp32s3-elf-gcc.exe", "--compile-commands-dir=f:\\Code\\ESP32_Project\\Test\\bmi270_esp\\sample_project\\build"]}