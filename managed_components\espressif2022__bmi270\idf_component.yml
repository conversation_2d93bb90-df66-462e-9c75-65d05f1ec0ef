dependencies:
  cmake_utilities:
    version: 0.*
  espressif/i2c_bus: 1.*
  idf:
    version: '>=4.4'
description: The BMI270 is a compact, low-power IMU designed for mobile applications.
documentation: https://github.com/boschsensortec/BMI270_SensorAPI/
issues: https://github.com/espressif2022/BMI270_SensorAPI/issues
repository: git://github.com/espressif2022/BMI270_SensorAPI.git
repository_info:
  commit_sha: 87fb95766e71bcec928894dd8d06ed867f11737b
  path: .
url: https://github.com/espressif2022/BMI270_SensorAPI
version: 1.1.0~2
